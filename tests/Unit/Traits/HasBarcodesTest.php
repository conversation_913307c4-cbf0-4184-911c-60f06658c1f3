<?php

namespace Tests\Unit\Traits;

use Tests\TestCase;
use App\Traits\HasBarcodes;
use App\Enums\Api\Internal\BarcodeEnum;
use Exception;
use InvalidArgumentException;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TestBarcodesClass
{
    use HasBarcodes;
}

class HasBarcodesTest extends TestCase
{
    use RefreshDatabase;

    private TestBarcodesClass $testClass;

    protected function setUp(): void
    {
        parent::setUp();
        $this->testClass = new TestBarcodesClass();
    }

    public function test_it_validates_ean8_barcode(): void
    {
        // Arrange
        $validEAN8 = '21500015';
        $invalidEAN8 = '21500014';
        $shortEAN8 = '1234567';
        $longEAN8 = '123456789';

        $result = $this->testClass->validateEAN8($validEAN8);
        $this->assertArrayHasKey('success', $result);
        $this->assertEquals('EAN-8 контрольная цифра верна', $result['message']);

        $result = $this->testClass->validateEAN8($shortEAN8);
        $this->assertArrayHasKey('errors', $result);
        $this->assertEquals('EAN-8 должен состоять из 8 цифр', $result['message']);

        $result = $this->testClass->validateEAN8($invalidEAN8);
        $this->assertArrayHasKey('errors', $result);
        $this->assertEquals('EAN-8 Неверная контрольная цифра', $result['message']);

        $result = $this->testClass->validateEAN8($longEAN8);
        $this->assertArrayHasKey('errors', $result);
        $this->assertEquals('EAN-8 должен состоять из 8 цифр', $result['message']);
    }

    public function test_it_validates_ean13_barcode(): void
    {
        // Arrange
        $validEAN13 = '4003994155486'; // Valid EAN-13 with correct checksum
        $invalidEAN13 = '4003994155487'; // Invalid EAN-13 with wrong checksum
        $shortEAN13 = '123456789012'; // Too short
        $longEAN13 = '12345678901234'; // Too long

        // Act & Assert
        // Valid EAN-13
        $result = $this->testClass->validateEAN13($validEAN13);
        $this->assertArrayHasKey('success', $result);
        $this->assertEquals('EAN-13 контрольная цифра верна', $result['message']);

        // Invalid EAN-13
        $result = $this->testClass->validateEAN13($invalidEAN13);
        $this->assertArrayHasKey('errors', $result);
        $this->assertEquals('EAN-13 Неверная контрольная цифра', $result['message']);

        // Too short
        $result = $this->testClass->validateEAN13($shortEAN13);
        $this->assertArrayHasKey('errors', $result);
        $this->assertEquals('EAN-13 должен состоять из 13 цифр', $result['message']);

        // Too long
        $result = $this->testClass->validateEAN13($longEAN13);
        $this->assertArrayHasKey('errors', $result);
        $this->assertEquals('EAN-13 должен состоять из 13 цифр', $result['message']);
    }

    public function test_it_validates_gtin_barcode(): void
    {
        // Arrange
        $validGTIN8 = '14567810'; // Valid GTIN-8
        $validGTIN13 = '4003994155486'; // Valid GTIN-13
        $validGTIN14 = '04003994155486'; // Valid GTIN-14
        $invalidGTIN = '123456789'; // Invalid length

        // Act & Assert
        // Valid GTIN-8
        $result = $this->testClass->validateGtinOrUpc($validGTIN8, BarcodeEnum::GTIN->value);
        $this->assertArrayHasKey('success', $result);
        $this->assertEquals('GTIN контрольная сумма верна', $result['message']);

        // Valid GTIN-13
        $result = $this->testClass->validateGtinOrUpc($validGTIN13, BarcodeEnum::GTIN->value);
        $this->assertArrayHasKey('success', $result);
        $this->assertEquals('GTIN контрольная сумма верна', $result['message']);

        // Valid GTIN-14
        $result = $this->testClass->validateGtinOrUpc($validGTIN14, BarcodeEnum::GTIN->value);
        $this->assertArrayHasKey('success', $result);
        $this->assertEquals('GTIN контрольная сумма верна', $result['message']);

        // Invalid GTIN
        $this->expectException(Exception::class);
        $this->testClass->validateGtinOrUpc($invalidGTIN, BarcodeEnum::GTIN->value);
    }

    public function test_it_validates_upc_barcode(): void
    {
        // Arrange
        $validUPC = '212045648905'; // Valid UPC
        $invalidUPC = '400399415549'; // Invalid UPC
        $shortUPC = '12345678901'; // Too short
        $longUPC = '1234567890123'; // Too long

        // Act & Assert
        // Valid UPC
        $result = $this->testClass->validateGtinOrUpc($validUPC, BarcodeEnum::UPC->value);
        $this->assertEquals('UPC контрольная сумма верна', $result['message']);

        // Invalid UPC
        $result = $this->testClass->validateGtinOrUpc($invalidUPC, BarcodeEnum::UPC->value);
        $this->assertArrayHasKey('errors', $result);
        $this->assertEquals('UPC Неверная контрольная сумма', $result['message']);

        // Too short
        $this->expectException(Exception::class);
        $this->testClass->validateGtinOrUpc($shortUPC, BarcodeEnum::UPC->value);

        // Too long
        $this->expectException(Exception::class);
        $this->testClass->validateGtinOrUpc($longUPC, BarcodeEnum::UPC->value);
    }

    public function test_it_validates_code128_barcode(): void
    {
        // Arrange
        $validCode128 = 'ABC123!@#';
        $invalidCode128 = 'ABC123Привет'; // Contains Cyrillic
        $longCode128 = str_repeat('A', 31); // 31 characters
        $nullCode128 = null;

        // Act & Assert
        // Valid CODE128
        $this->assertTrue($this->testClass->validateCODE128($validCode128, BarcodeEnum::CODE128->value));

        // Invalid CODE128 (Cyrillic)
        $this->expectException(Exception::class);
        $this->testClass->validateCODE128($invalidCode128, BarcodeEnum::CODE128->value);

        // Long CODE128
        $this->expectException(Exception::class);
        $this->testClass->validateCODE128($longCode128, BarcodeEnum::CODE128->value);

        // Null CODE128
        $this->expectException(Exception::class);
        $this->testClass->validateCODE128($nullCode128, BarcodeEnum::CODE128->value);
    }

    /**
     * @throws Exception
     */
    public function test_it_generates_next_ean8(): void
    {
        // Arrange
        $lastEAN8 = '40039941';
        $expectedNext = '40039942';

        // Act
        $nextEAN8 = $this->testClass->generateNextEAN8($lastEAN8);

        // Assert
        $this->assertEquals($expectedNext, $nextEAN8);
    }

    /**
     * @throws Exception
     */
    public function test_it_generates_next_ean13(): void
    {
        // Arrange
        $lastEAN13 = '4003994155486';
        $expectedNext = '4003994155487';

        // Act
        $nextEAN13 = $this->testClass->generateNextEAN13($lastEAN13);

        // Assert
        $this->assertEquals($expectedNext, $nextEAN13);
    }

    public function test_it_throws_exception_when_ean8_exceeds_length(): void
    {
        // Arrange
        $lastEAN8 = '99999999';

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Штрихкод превышает допустимую длину (8 символов).');
        $this->testClass->generateNextEAN8($lastEAN8);
    }

    public function test_it_throws_exception_when_ean13_exceeds_length(): void
    {
        // Arrange
        $lastEAN13 = '9999999999999';

        // Act & Assert
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Штрихкод превышает допустимую длину (13 символов).');
        $this->testClass->generateNextEAN13($lastEAN13);
    }

    public function test_it_generates_gtin_with_different_lengths(): void
    {
        // Arrange
        $gtin8 = '40039941';
        $gtin13 = '4003994155486';
        $gtin14 = '04003994155486';
        $invalidGtin = '123456789';

        // Act & Assert
        // GTIN-8
        $result = $this->testClass->generateGTIN($gtin8);
        $this->assertIsString($result);
        $this->assertEquals(8, strlen($result));

        // GTIN-13
        $result = $this->testClass->generateGTIN($gtin13);
        $this->assertIsString($result);
        $this->assertEquals(13, strlen($result));

        // GTIN-14
        $result = $this->testClass->generateGTIN($gtin14);
        $this->assertIsString($result);
        $this->assertEquals(14, strlen($result));

        // Invalid GTIN
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('GTIN должен быть длиной 8, 12, 13 или 14 цифр.');
        $this->testClass->generateGTIN($invalidGtin);
    }
}
