<?php

namespace Tests\Unit\Services\Api\Internal\FifoService\Handlers;

use Tests\TestCase;
use Mockery;
use RuntimeException;
use Illuminate\Support\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\Api\Internal\FifoService\Handlers\FifoHandler;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentWarehouseItemRepositoryContract;
use Illuminate\Foundation\Testing\WithFaker;

class FifoHandlerTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;

    private FifoHandler $handler;
    private ShipmentItemsRepositoryContract $shipmentItemsRepository;
    private WarehouseItemsRepositoryContract $warehouseItemsRepository;
    private ShipmentWarehouseItemRepositoryContract $shipmentWarehouseItemRepository;
    private ShipmentsRepositoryContract $shipmentsRepository;
    private DocumentsRepositoryContract $documentsRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->shipmentItemsRepository = Mockery::mock(ShipmentItemsRepositoryContract::class);
        $this->warehouseItemsRepository = Mockery::mock(WarehouseItemsRepositoryContract::class);
        $this->shipmentWarehouseItemRepository = Mockery::mock(ShipmentWarehouseItemRepositoryContract::class);
        $this->shipmentsRepository = Mockery::mock(ShipmentsRepositoryContract::class);
        $this->documentsRepository = Mockery::mock(DocumentsRepositoryContract::class);

        $this->handler = new FifoHandler(
            $this->shipmentItemsRepository,
            $this->warehouseItemsRepository,
            $this->shipmentWarehouseItemRepository,
            $this->shipmentsRepository,
            $this->documentsRepository
        );
    }

    public function test_run_throws_exception_when_shipment_not_found(): void
    {
        // Arrange
        $resourceId = $this->faker()->uuid;

        $this->shipmentItemsRepository
            ->shouldReceive('getShipmentDetails')
            ->once()
            ->with($resourceId, ['shipments.date_from', 'shipments.warehouse_id', 'shipment_items.product_id'])
            ->andReturnNull();

        // We should not expect getNewestShipmentItems to be called if getShipmentDetails returns null
        $this->shipmentItemsRepository
            ->shouldNotReceive('getNewestShipmentItems');

        // Assert
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Shipment not found');

        // Act
        $this->handler->run($resourceId);
    }

    public function test_run_skips_when_no_newest_shipment_items(): void
    {
        // Arrange
        $resourceId = $this->faker()->uuid;
        $shipmentDetails = (object)[
            'date_from' => Carbon::now(),
            'warehouse_id' => $this->faker()->uuid,
            'product_id' => $this->faker()->uuid
        ];

        $this->shipmentItemsRepository
            ->shouldReceive('getShipmentDetails')
            ->once()
            ->with($resourceId, ['shipments.date_from', 'shipments.warehouse_id', 'shipment_items.product_id'])
            ->andReturn($shipmentDetails);

        $this->shipmentItemsRepository
            ->shouldReceive('getNewestShipmentItems')
            ->once()
            ->withAnyArgs()
            ->andReturnNull();

        // Act
        $this->handler->run($resourceId);

        // Assert
        $this->warehouseItemsRepository->shouldNotHaveReceived('returnWarehouseItemsFromShipmentItemIds');
        $this->shipmentWarehouseItemRepository->shouldNotHaveReceived('deleteWhereInIds');

        $this->assertTrue(true);
    }

    public function test_run_handles_fifo_correctly(): void
    {
        // Arrange
        $resourceId = $this->faker()->uuid;
        $shipmentId = $this->faker()->uuid;
        $shipmentDetails = (object)[
            'date_from' => Carbon::now(),
            'warehouse_id' => $this->faker()->uuid,
            'product_id' => $this->faker()->uuid
        ];

        $newestShipmentItems = collect([
            (object)[
                'id' => $resourceId,
                'shipment_id' => $shipmentId,
                'product_id' => $shipmentDetails->product_id,
                'quantity' => 10,
                'total_price' => 1000,
                'shipment_date' => $shipmentDetails->date_from,
                'updated_at' => Carbon::now()
            ]
        ]);

        $warehouseItems = collect([
            (object)[
                'id' => $this->faker()->uuid,
                'quantity' => 15,
                'unit_price' => 50,
                'total_price' => 750,
                'created_at' => Carbon::now(),
                'warehouse_id' => $shipmentDetails->warehouse_id,
                'cell_id' => $this->faker()->uuid,
                'product_id' => $shipmentDetails->product_id,
                'acceptance_id' => $this->faker()->uuid,
                'batch_number' => $this->faker()->uuid,
                'received_at' => Carbon::now(),
                'status' => 'in_stock'
            ]
        ]);

        $this->shipmentItemsRepository
            ->shouldReceive('getShipmentDetails')
            ->once()
            ->with($resourceId, ['shipments.date_from', 'shipments.warehouse_id', 'shipment_items.product_id'])
            ->andReturn($shipmentDetails);

        $this->shipmentItemsRepository
            ->shouldReceive('getNewestShipmentItems')
            ->once()
            ->withAnyArgs()
            ->andReturn($newestShipmentItems);

        $this->warehouseItemsRepository
            ->shouldReceive('returnWarehouseItemsFromShipmentItemIds')
            ->once()
            ->with([$resourceId]);

        $this->shipmentWarehouseItemRepository
            ->shouldReceive('deleteWhereInIds')
            ->once()
            ->with([$resourceId]);

        $this->warehouseItemsRepository
            ->shouldReceive('get')
            ->once()
            ->withAnyArgs()
            ->andReturn($warehouseItems);

        $this->warehouseItemsRepository
            ->shouldReceive('upsert')
            ->once()
            ->withArgs(function ($items, $uniqueBy) {
                return count($items) === 1
                    && $items[0]['quantity'] === 5
                    && $items[0]['status'] === 'in_stock';
            });

        $this->shipmentItemsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($id, $data) use ($resourceId) {
                return $id === $resourceId
                    && $data['total_cost'] === 500
                    && $data['cost'] === 50
                    && $data['profit'] === 500
                    && $data['recidual'] === 15;
            });

        $this->shipmentWarehouseItemRepository
            ->shouldReceive('insert')
            ->once()
            ->withArgs(function ($data) {
                return count($data) === 1
                    && $data[0]['quantity'] === 10;
            });

        $this->shipmentsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($id, $data) use ($shipmentId) {
                return $id === $shipmentId
                    && $data['total_cost'] === 500
                    && $data['profit'] === 500;
            });

        // Act
        $this->handler->run($resourceId);


        $this->assertTrue(true);
    }

    public function test_handle_bulk_throws_exception_when_shipments_not_found(): void
    {
        // Arrange
        $shipmentItemIds = [$this->faker()->uuid];

        $this->shipmentItemsRepository
            ->shouldReceive('getBulkShipmentDetails')
            ->once()
            ->with($shipmentItemIds, ['shipments.date_from', 'shipments.warehouse_id', 'shipment_items.product_id'])
            ->andReturn(collect());

        // Assert
        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Shipments not found');

        // Act
        $this->handler->handleBulk($shipmentItemIds);
    }

    public function test_handle_bulk_processes_multiple_shipments_correctly(): void
    {
        // Arrange
        // Создаем реальную отгрузку в БД
        $shipment = \App\Models\Shipment::factory()->create([
            'date_from' => Carbon::now(),
        ]);

        $shipmentItemIds = [];

        // Создаем два shipment items в БД
        for ($i = 0; $i < 2; $i++) {
            $shipmentItem = \App\Models\ShipmentItem::factory()->create([
                'shipment_id' => $shipment->id,
                'quantity' => $i === 0 ? 10 : 15,
                'total_price' => $i === 0 ? 1000 : 1500,
            ]);
            $shipmentItemIds[] = $shipmentItem->id;
        }

        $warehouseId = $shipment->warehouse_id;
        $date = $shipment->date_from;
        $productId = $shipmentItem->product_id;
        $shipmentId = $shipment->id;

        $shipmentDetails = collect([
            (object)[
                'warehouse_id' => $warehouseId,
                'date_from' => $date,
                'product_id' => $productId,
                'shipment_id' => $shipmentId
            ],
            (object)[
                'warehouse_id' => $warehouseId,
                'date_from' => $date,
                'product_id' => $productId,
                'shipment_id' => $shipmentId
            ]
        ]);

        $newestShipmentItems = collect([
            (object)[
                'id' => $shipmentItemIds[0],
                'shipment_id' => $shipmentId,
                'product_id' => $productId,
                'quantity' => 10,
                'total_price' => 1000,
                'shipment_date' => $date,
                'updated_at' => Carbon::now()
            ],
            (object)[
                'id' => $shipmentItemIds[1],
                'shipment_id' => $shipmentId,
                'product_id' => $productId,
                'quantity' => 15,
                'total_price' => 1500,
                'shipment_date' => $date,
                'updated_at' => Carbon::now()
            ]
        ]);

        $warehouseItems = collect([
            (object)[
                'id' => $this->faker()->uuid,
                'quantity' => 30,
                'unit_price' => 50,
                'total_price' => 1500,
                'created_at' => Carbon::now(),
                'warehouse_id' => $warehouseId,
                'cell_id' => $this->faker()->uuid,
                'product_id' => $productId,
                'acceptance_id' => $this->faker()->uuid,
                'batch_number' => $this->faker()->uuid,
                'received_at' => Carbon::now(),
                'status' => 'in_stock'
            ]
        ]);

        $this->shipmentItemsRepository
            ->shouldReceive('getBulkShipmentDetails')
            ->once()
            ->with($shipmentItemIds, ['shipments.date_from', 'shipments.warehouse_id', 'shipment_items.product_id'])
            ->andReturn($shipmentDetails);

        $this->shipmentItemsRepository
            ->shouldReceive('getBulkNewestShipmentItems')
            ->once()
            ->withAnyArgs()
            ->andReturn($newestShipmentItems);

        $this->warehouseItemsRepository
            ->shouldReceive('returnWarehouseItemsFromShipmentItemIds')
            ->once()
            ->with($shipmentItemIds);

        $this->shipmentWarehouseItemRepository
            ->shouldReceive('deleteWhereInIds')
            ->once()
            ->with($shipmentItemIds);

        $this->warehouseItemsRepository
            ->shouldReceive('get')
            ->once()
            ->withAnyArgs()
            ->andReturn($warehouseItems);

        $this->warehouseItemsRepository
            ->shouldReceive('upsert')
            ->once()
            ->withAnyArgs();

        $this->shipmentWarehouseItemRepository
            ->shouldReceive('insert')
            ->once()
            ->withArgs(function ($data) {
                return count($data) === 2
                    && $data[0]['quantity'] === 10
                    && $data[1]['quantity'] === 15;
            });

        // Ожидаем обновление через репозиторий вместо прямого обращения к БД
        $this->shipmentItemsRepository
            ->shouldReceive('bulkUpdate')
            ->once()
            ->withArgs(function ($items) use ($shipmentItemIds) {
                return count($items) === 2
                    && isset($items[0]['id'])
                    && isset($items[1]['id'])
                    && in_array($items[0]['id'], $shipmentItemIds)
                    && in_array($items[1]['id'], $shipmentItemIds);
            });

        // Ожидаем обновление shipment
        $this->shipmentsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($id, $data) use ($shipmentId) {
                return $id === $shipmentId
                    && isset($data['total_cost'])
                    && isset($data['profit']);
            });

        // Act
        $this->handler->handleBulk($shipmentItemIds);

        // Assert
        $this->assertTrue(true);
    }

    public function test_handle_bulk_with_delete_removes_items(): void
    {
        // Arrange
        $shipmentItemIds = [$this->faker()->uuid];
        $warehouseId = $this->faker()->uuid;
        $date = Carbon::now();
        $productId = $this->faker()->uuid;

        $shipmentDetails = collect([
            (object)[
                'warehouse_id' => $warehouseId,
                'date_from' => $date,
                'product_id' => $productId
            ]
        ]);

        $newestShipmentItems = collect([
            (object)[
                'id' => $shipmentItemIds[0],
                'shipment_id' => $this->faker()->uuid,
                'product_id' => $productId,
                'quantity' => 10,
                'total_price' => 1000,
                'shipment_date' => $date,
                'updated_at' => Carbon::now()
            ]
        ]);

        $this->shipmentItemsRepository
            ->shouldReceive('getBulkShipmentDetails')
            ->once()
            ->with($shipmentItemIds, ['shipments.date_from', 'shipments.warehouse_id', 'shipment_items.product_id'])
            ->andReturn($shipmentDetails);

        $this->shipmentItemsRepository
            ->shouldReceive('getBulkNewestShipmentItems')
            ->once()
            ->withAnyArgs()
            ->andReturn($newestShipmentItems);

        $this->warehouseItemsRepository
            ->shouldReceive('returnWarehouseItemsFromShipmentItemIds')
            ->once()
            ->with($shipmentItemIds);

        $this->shipmentWarehouseItemRepository
            ->shouldReceive('deleteWhereInIds')
            ->once()
            ->with($shipmentItemIds);

        $this->shipmentItemsRepository
            ->shouldReceive('bulkDelete')
            ->once()
            ->with($shipmentItemIds);

        $this->documentsRepository
            ->shouldReceive('deleteWhereDocumentableIdIn')
            ->once()
            ->with($shipmentItemIds);

        // Для проверки handleBulkFIFO, который вызывается с пустой коллекцией
        // после удаления элементов
        $this->warehouseItemsRepository
            ->shouldNotReceive('get');

        $this->warehouseItemsRepository
            ->shouldNotReceive('upsert');

        $this->shipmentWarehouseItemRepository
            ->shouldNotReceive('insert');

        // Act
        $this->handler->handleBulk($shipmentItemIds, true);

        $this->assertTrue(true);
    }

    /**
     * Этот тест проверяет, что система FIFO правильно обрабатывает списание товаров
     * из нескольких партий с разными ценами в правильном порядке
     */
    public function test_fifo_correctly_processes_multiple_batches_with_different_prices(): void
    {
        // Arrange
        $resourceId = $this->faker()->uuid;
        $shipmentId = $this->faker()->uuid;
        $shipmentDetails = (object)[
            'date_from' => Carbon::now(),
            'warehouse_id' => $this->faker()->uuid,
            'product_id' => $this->faker()->uuid
        ];

        $newestShipmentItems = collect([
            (object)[
                'id' => $resourceId,
                'shipment_id' => $shipmentId,
                'product_id' => $shipmentDetails->product_id,
                'quantity' => 25, // Потребуется товар из нескольких партий
                'total_price' => 2500, // Общая стоимость отгрузки
                'shipment_date' => $shipmentDetails->date_from,
                'updated_at' => Carbon::now()
            ]
        ]);

        // Создаем три партии товаров с разными датами поступления и ценами
        $batchDate1 = Carbon::now()->subDays(10);
        $batchDate2 = Carbon::now()->subDays(5);
        $batchDate3 = Carbon::now()->subDays(2);

        // Три партии товаров с разными ценами
        $warehouseItems = collect([
            (object)[
                'id' => $this->faker()->uuid,
                'quantity' => 10,
                'unit_price' => 40, // Старая партия с низкой ценой
                'total_price' => 400,
                'created_at' => $batchDate1,
                'warehouse_id' => $shipmentDetails->warehouse_id,
                'cell_id' => $this->faker()->uuid,
                'product_id' => $shipmentDetails->product_id,
                'acceptance_id' => $this->faker()->uuid,
                'batch_number' => $this->faker()->uuid,
                'received_at' => $batchDate1,
                'status' => 'in_stock'
            ],
            (object)[
                'id' => $this->faker()->uuid,
                'quantity' => 10,
                'unit_price' => 50, // Средняя партия со средней ценой
                'total_price' => 500,
                'created_at' => $batchDate2,
                'warehouse_id' => $shipmentDetails->warehouse_id,
                'cell_id' => $this->faker()->uuid,
                'product_id' => $shipmentDetails->product_id,
                'acceptance_id' => $this->faker()->uuid,
                'batch_number' => $this->faker()->uuid,
                'received_at' => $batchDate2,
                'status' => 'in_stock'
            ],
            (object)[
                'id' => $this->faker()->uuid,
                'quantity' => 10,
                'unit_price' => 60, // Новая партия с высокой ценой
                'total_price' => 600,
                'created_at' => $batchDate3,
                'warehouse_id' => $shipmentDetails->warehouse_id,
                'cell_id' => $this->faker()->uuid,
                'product_id' => $shipmentDetails->product_id,
                'acceptance_id' => $this->faker()->uuid,
                'batch_number' => $this->faker()->uuid,
                'received_at' => $batchDate3,
                'status' => 'in_stock'
            ]
        ]);

        $this->shipmentItemsRepository
            ->shouldReceive('getShipmentDetails')
            ->once()
            ->with($resourceId, ['shipments.date_from', 'shipments.warehouse_id', 'shipment_items.product_id'])
            ->andReturn($shipmentDetails);

        $this->shipmentItemsRepository
            ->shouldReceive('getNewestShipmentItems')
            ->once()
            ->withAnyArgs()
            ->andReturn($newestShipmentItems);

        $this->warehouseItemsRepository
            ->shouldReceive('returnWarehouseItemsFromShipmentItemIds')
            ->once()
            ->with([$resourceId]);

        $this->shipmentWarehouseItemRepository
            ->shouldReceive('deleteWhereInIds')
            ->once()
            ->with([$resourceId]);

        $this->warehouseItemsRepository
            ->shouldReceive('get')
            ->once()
            ->withAnyArgs()
            ->andReturn($warehouseItems);

        // Проверяем, что обновляются все три партии товаров
        $this->warehouseItemsRepository
            ->shouldReceive('upsert')
            ->once()
            ->withArgs(function ($items, $uniqueBy) {
                // Проверяем, что первые две партии полностью израсходованы, а от третьей взято 5 единиц
                return count($items) === 3
                    && $items[0]['quantity'] === 0
                    && $items[0]['status'] === 'out_of_stock'
                    && $items[1]['quantity'] === 0
                    && $items[1]['status'] === 'out_of_stock'
                    && $items[2]['quantity'] === 5
                    && $items[2]['status'] === 'in_stock';
            });

        // Проверяем правильность вычисления стоимости по FIFO
        // Расчет: (10 * 40) + (10 * 50) + (5 * 60) = 400 + 500 + 300 = 1200
        $expectedTotalCost = 1200;
        $expectedProfitValue = 2500 - 1200; // 1300

        $this->shipmentItemsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($id, $data) use ($resourceId, $expectedTotalCost, $expectedProfitValue) {
                return $id === $resourceId
                    && $data['total_cost'] === $expectedTotalCost
                    && $data['cost'] === $expectedTotalCost / 25 // Средняя стоимость единицы
                    && $data['profit'] === $expectedProfitValue
                    && $data['recidual'] === 30; // Общее количество товаров в партиях
            });

        $this->shipmentWarehouseItemRepository
            ->shouldReceive('insert')
            ->once()
            ->withArgs(function ($data) {
                // Проверяем, что добавлены записи для всех трех партий
                return count($data) === 3
                    && $data[0]['quantity'] === 10
                    && $data[1]['quantity'] === 10
                    && $data[2]['quantity'] === 5;
            });

        $this->shipmentsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($id, $data) use ($shipmentId, $expectedTotalCost, $expectedProfitValue) {
                return $id === $shipmentId
                    && $data['total_cost'] === $expectedTotalCost
                    && $data['profit'] === $expectedProfitValue;
            });

        // Act
        $this->handler->run($resourceId);

        $this->assertTrue(true);
    }
}
