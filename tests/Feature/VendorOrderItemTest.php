<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\Product;
use App\Models\Employee;
use App\Models\VatRate;
use App\Models\VendorOrder;
use App\Models\Department;
use App\Models\VendorOrderItem;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class VendorOrderItemTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_vendor_order_items_list(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем 3 позиции для нашего заказа
        VendorOrderItem::factory()->count(3)->create([
            'order_id' => $order->id
        ]);

        // Создаем позицию для другого заказа, чтобы убедиться что не получим её
        $otherOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        VendorOrderItem::factory()->create([
            'order_id' => $otherOrder->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/vendor-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'order_id',
                        'product_id',
                        'quantity',
                        'price',
                        'vat_rate_id',
                        'discount',
                        'total_price'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем что получили только 3 записи нашего заказа
        $response->assertJsonCount(3, 'data');
        foreach ($response->json('data') as $item) {
            $this->assertEquals($order->id, $item['order_id']);
        }

        // Проверяем что в мета-данных общее количество равно 3
        $this->assertEquals(3, $response->json('meta.total'));
    }

    public function test_cannot_access_other_cabinet_vendor_order_items(): void
    {
        // Arrange
        // Создаем заказ в другом кабинете
        $otherCabinetOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Создаем позиции для заказа другого кабинета
        VendorOrderItem::factory()->count(2)->create([
            'order_id' => $otherCabinetOrder->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/vendor-orders/items?' . http_build_query([
            'order_id' => $otherCabinetOrder->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertNotFound();
    }

    public function test_index_validation_errors(): void
    {
        $response = $this->getJson('/api/internal/vendor-orders/items?' . http_build_query([
            'page' => 0, // Неверное значение
            'per_page' => 101, // Превышает максимум
            'sortDirection' => 'invalid', // Неверное значение
            'order_id' => 'not-a-uuid' // Неверный формат UUID
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'order_id',
                'page',
                'per_page',
                'sortDirection'
            ]);
    }

    public function test_index_with_valid_parameters(): void
    {
        $order = VendorOrder::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        VendorOrderItem::factory()->count(2)
            ->create(['order_id' => $order->id]);

        $response = $this->getJson('/api/internal/vendor-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'page' => 1,
            'per_page' => 15,
            'sortField' => 'created_at',
            'sortDirection' => 'desc',
            'fields' => ['id', 'created_at']
        ]));

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at'
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        // Проверяем количество записей
        $response->assertJsonCount(2, 'data');
    }

    public function test_index_without_required_order_id(): void
    {
        $response = $this->getJson('/api/internal/vendor-orders/items');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['order_id']);
    }

    public function test_index_with_invalid_sort_field(): void
    {
        $order = VendorOrder::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        $response = $this->getJson('/api/internal/vendor-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'sortField' => 'invalid_field',
            'sortDirection' => 'asc'
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sortField']);
    }

    public function test_index_with_invalid_fields(): void
    {
        $order = VendorOrder::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        $response = $this->getJson('/api/internal/vendor-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'fields' => ['invalid_field', 'another_invalid_field']
        ]));

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['fields.0', 'fields.1']);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        $order = VendorOrder::factory()
            ->create(['cabinet_id' => $this->cabinet->id]);

        // Создаем позиции с разными ценами
        $item1 = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'price' => 100
        ]);
        $item2 = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'price' => 200
        ]);
        $item3 = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'price' => 150
        ]);

        // Act - получаем отсортированный по цене список
        $response = $this->getJson('/api/internal/vendor-orders/items?' . http_build_query([
            'order_id' => $order->id,
            'sortField' => 'price',
            'sortDirection' => 'asc',
            'fields' => ['id', 'price']
        ]));

        // Assert
        $response->assertStatus(200);

        $prices = collect($response->json('data'))->pluck('price')->values();
        $expectedPrices = collect([$item1->price, $item3->price, $item2->price]);

        $this->assertEquals($expectedPrices, $prices);
    }

    public function test_can_create_vendor_order_item(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $vatRate = VatRate::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => 2,
            'price_in_currency' => '123',
            'currency_rate_to_base' => '1',
            'currency_id' => $currency->id,
            'discount' => '10',
            'vat_rate_id' => $vatRate->id,
        ];

        // Act
        $response = $this->postJson('/api/internal/vendor-orders/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('vendor_order_items', [
            'id' => $response->json('id'),
            'order_id' => $data['order_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
            'price_in_currency' => $data['price_in_currency'],
            'discount' => $data['discount'],
        ]);

        // Проверяем что запись создана в базе
        $item = VendorOrderItem::find($response->json('id'));
        $this->assertNotNull($item);
    }

    public function test_cannot_create_vendor_order_item_for_other_cabinet(): void
    {
        // Arrange
        $otherCabinetOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'order_id' => $otherCabinetOrder->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => random_int(0, 10000),
        ];

        // Act
        $response = $this->postJson('/api/internal/vendor-orders/items', $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('vendor_order_items', [
            'order_id' => $data['order_id'],
            'product_id' => $data['product_id'],
        ]);
    }

    public function test_cannot_create_vendor_order_item_with_invalid_data(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $invalidData = [
            'order_id' => $order->id,
            'product_id' => 'invalid-uuid',
            'quantity' => -1, // отрицательное количество
            'price' => 'not-a-number', // неверный формат цены
            'vat_rate_id' => 'invalid-uuid',
            'discount' => 'not-a-number',
        ];

        // Act
        $response = $this->postJson('/api/internal/vendor-orders/items', $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'product_id',
                'quantity',
                'price',
                'vat_rate_id',
                'discount',
            ]);
    }

    public function test_cannot_create_vendor_order_item_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/vendor-orders/items', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'order_id',
                'product_id',
                'quantity'
            ]);
    }

    public function test_can_create_vendor_order_item_with_minimal_required_fields(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => $this->faker->numberBetween(1, 100),
        ];

        // Act
        $response = $this->postJson('/api/internal/vendor-orders/items', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('vendor_order_items', [
            'id' => $response->json('id'),
            'order_id' => $data['order_id'],
            'product_id' => $data['product_id'],
            'quantity' => $data['quantity'],
        ]);
    }

    public function test_cannot_create_vendor_order_item_with_product_from_other_cabinet(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherCabinetProduct = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'order_id' => $order->id,
            'product_id' => $otherCabinetProduct->id,
            'quantity' => $this->faker->numberBetween(1, 100),
            'price' => random_int(0, 9999999),
        ];

        // Act
        $response = $this->postJson('/api/internal/vendor-orders/items', $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('vendor_order_items', [
            'order_id' => $data['order_id'],
            'product_id' => $data['product_id'],
        ]);
    }

    public function test_create_vendor_order_item_updates_order_total_price(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'total_price' => 0
        ]);

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => 2,
            'price' => 100,
            'discount' => 10, // 10% скидка
        ];

        // Act
        $response = $this->postJson('/api/internal/vendor-orders/items', $data);

        // Assert
        $response->assertStatus(201);

        // Проверяем что total_price заказа обновился
        $expectedTotalPrice = ($data['price'] * $data['quantity']) * (1 - $data['discount'] / 100); // 180
        $this->assertDatabaseHas('vendor_orders', [
            'id' => $order->id,
            'total_price' => $expectedTotalPrice
        ]);
    }

    public function test_can_update_vendor_order_item(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $item = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'quantity' => 1,
            'price' => 100,
            'discount' => 0,
            'total_price' => 100
        ]);

        $updateData = [
            'quantity' => 2,
            'price' => 150,
            'discount' => 10,
        ];

        // Act
        $response = $this->putJson("/api/internal/vendor-orders/items/{$item->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('vendor_order_items', [
            'id' => $item->id,
            'quantity' => $updateData['quantity'],
            'price' => $updateData['price'],
            'discount' => $updateData['discount'],
        ]);

        // Проверяем что total_price обновился корректно
        $updatedItem = VendorOrderItem::find($item->id);
        $expectedTotalPrice = ($updateData['price'] * $updateData['quantity']) * (1 - $updateData['discount'] / 100);
        $this->assertEquals((int)$expectedTotalPrice, $updatedItem->total_price);
    }

    public function test_can_update_vendor_order_item_with_minimal_data(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $item = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'quantity' => 1,
            'price' => 100,
            'discount' => 0,
            'total_price' => 100
        ]);

        $updateData = [
            'quantity' => 2,
        ];

        // Act
        $response = $this->putJson("/api/internal/vendor-orders/items/{$item->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('vendor_order_items', [
            'id' => $item->id,
            'quantity' => $updateData['quantity'],
        ]);
    }

    public function test_cannot_update_vendor_order_item_from_other_cabinet(): void
    {
        // Arrange
        $otherOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherItem = VendorOrderItem::factory()->create([
            'order_id' => $otherOrder->id
        ]);

        $updateData = [
            'quantity' => 2,
            'price' => 150,
        ];

        // Act
        $response = $this->putJson("/api/internal/vendor-orders/items/{$otherItem->id}", $updateData);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseMissing('vendor_order_items', [
            'id' => $otherItem->id,
            'quantity' => $updateData['quantity'],
            'price' => $updateData['price'],
        ]);
    }

    public function test_cannot_update_vendor_order_item_with_invalid_data(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $item = VendorOrderItem::factory()->create([
            'order_id' => $order->id
        ]);

        $invalidData = [
            'quantity' => -1, // отрицательное количество
            'price' => -100, // отрицательная цена
            'discount' => 'not-a-number', // неверный формат скидки
            'vat_rate_id' => 'invalid-uuid', // неверный формат UUID
        ];

        // Act
        $response = $this->putJson("/api/internal/vendor-orders/items/{$item->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'quantity',
                'price',
                'discount',
                'vat_rate_id'
            ]);
    }

    public function test_cannot_update_non_existent_vendor_order_item(): void
    {
        // Act
        $response = $this->putJson("/api/internal/vendor-orders/items/" . $this->faker->uuid(), [
            'quantity' => 2,
            'price' => 150,
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_update_vendor_order_item_updates_order_total_price(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'total_price' => 100
        ]);

        $item = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'quantity' => 1,
            'price' => 100,
            'discount' => 0,
            'total_price' => 100
        ]);

        $updateData = [
            'quantity' => 2,
            'price' => 200,
            'discount' => 10, // 10% скидка
        ];

        // Act
        $response = $this->putJson("/api/internal/vendor-orders/items/{$item->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        // Проверяем что total_price заказа обновился
        $expectedTotalPrice = ($updateData['price'] * $updateData['quantity']) * (1 - $updateData['discount'] / 100); // 180
        $this->assertDatabaseHas('vendor_orders', [
            'id' => $order->id,
            'total_price' => $expectedTotalPrice
        ]);
    }

    public function test_can_update_vendor_order_item_partially(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $item = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'quantity' => 1,
            'price' => 100,
            'discount' => 0
        ]);

        $updateData = [
            'quantity' => 2, // обновляем только количество
        ];

        // Act
        $response = $this->putJson("/api/internal/vendor-orders/items/{$item->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('vendor_order_items', [
            'id' => $item->id,
            'quantity' => $updateData['quantity'],
            'discount' => $item->discount,
        ]);
    }

    public function test_can_show_vendor_order_item(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $item = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'quantity' => 2,
            'price' => 100,
            'discount' => 10,
            'total_price' => 180
        ]);

        // Act
        $response = $this->getJson("/api/internal/vendor-orders/items/{$item->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'order_id',
                'product_id',
                'quantity',
                'price',
                'vat_rate_id',
                'discount',
                'total_price'
            ]);

        $this->assertEquals($item->id, $response->json('id'));
        $this->assertEquals($order->id, $response->json('order_id'));
        $this->assertEquals($item->quantity, $response->json('quantity'));
        $this->assertEquals($item->price, $response->json('price'));
        $this->assertEquals($item->discount, $response->json('discount'));
        $this->assertEquals($item->total_price, $response->json('total_price'));
    }

    public function test_cannot_show_vendor_order_item_from_other_cabinet(): void
    {
        // Arrange
        $otherOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherItem = VendorOrderItem::factory()->create([
            'order_id' => $otherOrder->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/vendor-orders/items/{$otherItem->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_vendor_order_item(): void
    {
        // Act
        $response = $this->getJson("/api/internal/vendor-orders/items/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_vendor_order_item(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'total_price' => 180
        ]);

        $item = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'quantity' => 2,
            'price' => 100,
            'discount' => 10,
            'total_price' => 180
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/vendor-orders/items/{$item->id}");

        // Assert
        $response->assertStatus(204);

        // Проверяем что запись удалена
        $this->assertDatabaseMissing('vendor_order_items', [
            'id' => $item->id
        ]);

        // Проверяем что total_price заказа уменьшился на сумму удаленной позиции
        $this->assertDatabaseHas('vendor_orders', [
            'id' => $order->id,
            'total_price' => 0 // 180 - 180 = 0
        ]);
    }

    public function test_cannot_delete_vendor_order_item_from_other_cabinet(): void
    {
        // Arrange
        $otherOrder = VendorOrder::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $otherItem = VendorOrderItem::factory()->create([
            'order_id' => $otherOrder->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/vendor-orders/items/{$otherItem->id}");

        // Assert
        $response->assertNotFound();

        // Проверяем что запись не была удалена
        $this->assertDatabaseHas('vendor_order_items', [
            'id' => $otherItem->id
        ]);
    }

    public function test_cannot_delete_non_existent_vendor_order_item(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/vendor-orders/items/" . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_delete_vendor_order_item_updates_order_total_price(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'total_price' => 280
        ]);

        // Создаем две позиции
        $item1 = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'quantity' => 2,
            'price' => 100,
            'discount' => 10,
            'total_price' => 180
        ]);

        $item2 = VendorOrderItem::factory()->create([
            'order_id' => $order->id,
            'quantity' => 1,
            'price' => 100,
            'discount' => 0,
            'total_price' => 100
        ]);

        // Act - удаляем первую позицию
        $response = $this->deleteJson("/api/internal/vendor-orders/items/{$item1->id}");

        // Assert
        $response->assertStatus(204);

        // Проверяем что total_price заказа уменьшился на сумму удаленной позиции
        $this->assertDatabaseHas('vendor_orders', [
            'id' => $order->id,
            'total_price' => 100 // 280 - 180 = 100
        ]);

        // Проверяем что первая позиция удалена, а вторая осталась
        $this->assertDatabaseMissing('vendor_order_items', [
            'id' => $item1->id
        ]);
        $this->assertDatabaseHas('vendor_order_items', [
            'id' => $item2->id
        ]);
    }

    public function test_delete_vendor_order_item_deletes_related_documents(): void
    {
        // Arrange
        $order = VendorOrder::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $item = VendorOrderItem::factory()->create([
            'order_id' => $order->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/vendor-orders/items/{$item->id}");

        // Assert
        $response->assertStatus(204);

        // Проверяем что связанные документы удалены
        $this->assertDatabaseMissing('documents', [
            'documentable_id' => $item->id
        ]);
    }
}
