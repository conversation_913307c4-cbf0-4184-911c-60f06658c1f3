<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\CabinetEmployee;
use App\Models\UserViewSetting;
use Tests\TestCase;
use App\Enums\Api\Internal\ResourcesEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserViewSettingTest extends TestCase
{
    use RefreshDatabase;

    public function test_view_settings(): void
    {
        $user = User::factory()->create();
        $employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $cabinet->id
        ]);

        $data = [
            'cabinet_id' => $cabinet->id,
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->get('/api/internal/view-settings?' . http_build_query($data));

        $response->assertStatus(200)
            ->assertJsonIsArray();
    }

    public function test_destroy_view_settings(): void
    {
        $user = User::factory()->create();
        $employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $cabinet->id
        ]);
        $setting = UserViewSetting::factory()
            ->create([
                'employee_id' => $employee->id,
                'cabinet_id' => $cabinet->id
            ]);

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->delete('/api/internal/view-settings/' . $setting->id);

        $response->assertNoContent();
        $this->assertDatabaseMissing('user_view_settings', [
            'id' => $setting->id
        ]);
    }

    public function test_create_or_update_valid_view_settings(): void
    {
        $user = User::factory()->create();
        $employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $cabinet->id
        ]);

        $data = [
            'cabinet_id' => $cabinet->id,
            'name' => ResourcesEnum::COUNTRIES->value,
            'settings' => [
                'test' => true
            ]
        ];

        $this->assertDatabaseMissing('user_view_settings', [
            'cabinet_id' => $cabinet->id,
            'name' => ResourcesEnum::COUNTRIES->value
        ]);

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->post('/api/internal/view-settings', $data);

        $response->assertNoContent();

        $this->assertDatabaseHas('user_view_settings', [
            'cabinet_id' => $cabinet->id,
            'name' => ResourcesEnum::COUNTRIES->value
        ])->assertDatabaseCount('user_view_settings', 1);
    }

    public function test_with_invalid_validation_view_setting(): void
    {
        $user = User::factory()->create();
        $employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);
        $cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);
        CabinetEmployee::factory()->create([
            'employee_id' => $employee->id,
            'cabinet_id' => $cabinet->id
        ]);

        $data = [
            'cabinet_id' => $cabinet->id,
            'name' => 'invalid_name',
            'settings' => [
                'test' => true
            ]
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->post('/api/internal/view-settings', $data);

        $response->assertJsonValidationErrorFor('name');

        $this->assertDatabaseMissing('user_view_settings', [
            'cabinet_id' => $cabinet->id,
            'name' => $data['name']
        ])->assertDatabaseCount('user_view_settings', 0);

        $data = [
            'test' => 0
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->post('/api/internal/view-settings', $data);

        $response->assertJsonValidationErrors(['name','cabinet_id', 'settings']);

        $this->assertDatabaseCount('user_view_settings', 0);
    }
}
