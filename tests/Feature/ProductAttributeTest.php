<?php

namespace Tests\Feature;

use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Product;
use App\Models\ProductAttribute;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ProductAttributeTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $user;
    protected Cabinet $cabinet;
    protected Cabinet $otherCabinet;
    protected Employee $employee;
    protected Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_product_attributes_list(): void
    {
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $attribute->id,
        ]);

        $productAttribute = ProductAttribute::factory()->create([
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $attributeValue->id,
        ]);

        $response = $this->getJson('/api/internal/product_attributes?' . http_build_query([
            'product_id' => $product->id,
        ]));

        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'product_id',
                        'attribute_id',
                        'attribute_values_id',
                        'copy',
                        'sort_order',
                    ],
                ],
            ])
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $productAttribute->id)
            ->assertJsonPath('data.0.product_id', $product->id)
            ->assertJsonPath('data.0.attribute_id', $attribute->id)
            ->assertJsonPath('data.0.attribute_values_id', $attributeValue->id);
    }

    public function test_cannot_get_product_attributes_without_product_id(): void
    {
        $response = $this->getJson('/api/internal/product_attributes');

        $response->assertStatus(422);
    }

    public function test_cannot_get_product_attributes_with_invalid_product_id(): void
    {
        $response = $this->getJson('/api/internal/product_attributes?' . http_build_query([
            'product_id' => 'invalid-uuid',
        ]));

        $response
            ->assertStatus(422)
            ->assertJsonValidationErrors([
                'product_id'
            ]);
    }

    public function test_cannot_access_other_cabinet_product_attributes(): void
    {
        $otherProduct = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson('/api/internal/product_attributes?' . http_build_query([
            'product_id' => $otherProduct->id,
        ]));

        $response->assertStatus(404);
    }

    public function test_can_create_product_attribute_with_minimal_fields(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $product->cabinet_id,
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $attribute->id,
        ]);

        // Act
        $response = $this->postJson('/api/internal/product_attributes', [
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $attributeValue->id,
            'sort_order' => 1
        ]);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('product_attributes', [
            'id' => $response->json('id'),
            'product_id' => $product->id,
        ]);
    }

    public function test_can_create_product_attribute_with_all_fields(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $attribute->id,
        ]);

        // Act
        $response = $this->postJson('/api/internal/product_attributes', [
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $attributeValue->id,
            'sort_order' => $this->faker()->randomNumber(),
        ]);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('product_attributes', [
            'id' => $response->json('id'),
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $attributeValue->id,
        ]);
    }

    public function test_cannot_create_product_attribute_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/product_attributes', []);

        // Assert
        $response
            ->assertStatus(422)
            ->assertJsonValidationErrors([
                'product_id',
            ]);
    }

    public function test_cannot_create_product_attribute_with_product_from_other_cabinet(): void
    {
        // Arrange
        $otherProduct = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $attribute->id,
        ]);

        // Act
        $response = $this->postJson('/api/internal/product_attributes', [
            'product_id' => $otherProduct->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $attributeValue->id,
            'cabinet_id' => $this->cabinet->id,
            'sort_order' => 1
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_create_product_attribute_with_attribute_from_other_cabinet(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $otherAttribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $otherAttribute->id,
        ]);

        // Act
        $response = $this->postJson('/api/internal/product_attributes', [
            'product_id' => $product->id,
            'attribute_id' => $otherAttribute->id,
            'attribute_values_id' => $attributeValue->id,
            'cabinet_id' => $this->cabinet->id,
            'sort_order' => 1
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_create_product_attribute_with_attribute_value_from_other_attribute(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $otherAttribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $otherAttributeValue = AttributeValue::factory()->create([
            'attribute_id' => $otherAttribute->id,
        ]);

        // Act
        $response = $this->postJson('/api/internal/product_attributes', [
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $otherAttributeValue->id,
            'cabinet_id' => $this->cabinet->id,
            'sort_order' => 1
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_update_product_attribute_with_minimal_fields(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $productAttribute = ProductAttribute::factory()->create([
            'product_id' => $product->id,
        ]);

        $newProduct = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->putJson('/api/internal/product_attributes/' . $productAttribute->id, [
            'product_id' => $newProduct->id,
            'attribute_values_id' => $productAttribute->attribute_values_id,
            'sort_order' => 2,
            'attribute_id' => $attribute->id,
        ]);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('product_attributes', [
            'id' => $productAttribute->id,
            'product_id' => $newProduct->id,
        ]);
    }

    public function test_can_update_product_attribute_with_all_fields(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $attribute->id,
        ]);

        $productAttribute = ProductAttribute::factory()->create([
            'product_id' => $product->id,
        ]);

        $newProduct = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $newAttribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $newAttributeValue = AttributeValue::factory()->create([
            'attribute_id' => $newAttribute->id,
        ]);

        // Act
        $response = $this->putJson('/api/internal/product_attributes/' . $productAttribute->id, [
            'product_id' => $newProduct->id,
            'attribute_id' => $newAttribute->id,
            'attribute_values_id' => $newAttributeValue->id,
            'sort_order' => 2,
        ]);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('product_attributes', [
            'id' => $productAttribute->id,
            'product_id' => $newProduct->id,
            'attribute_id' => $newAttribute->id,
            'attribute_values_id' => $newAttributeValue->id,
            'sort_order' => 2,
        ]);
    }

    public function test_cannot_update_product_attribute_without_required_fields(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $productAttribute = ProductAttribute::factory()->create([
            'product_id' => $product->id,
        ]);

        // Act
        $response = $this->putJson('/api/internal/product_attributes/' . $productAttribute->id, []);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'product_id',
            ]);
    }

    public function test_cannot_update_non_existent_product_attribute(): void
    {
        // Act
        $response = $this->putJson('/api/internal/product_attributes/' . $this->faker->uuid(), [
            'product_id' => $this->faker->uuid(),
            'attribute_id' => $this->faker->uuid(),
            'attribute_values_id' => $this->faker->uuid(),
            'sort_order' => 1,
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_product_attribute_with_product_from_other_cabinet(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $productAttribute = ProductAttribute::factory()->create([
            'product_id' => $product->id,
        ]);

        $otherProduct = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->putJson('/api/internal/product_attributes/' . $productAttribute->id, [
            'product_id' => $otherProduct->id,
            'attribute_id' => $productAttribute->attribute_id,
            'attribute_values_id' => $productAttribute->attribute_values_id,
            'sort_order' => 1,
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_product_attribute_with_attribute_from_other_cabinet(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $productAttribute = ProductAttribute::factory()->create([
            'product_id' => $product->id,
        ]);

        $otherAttribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->putJson('/api/internal/product_attributes/' . $productAttribute->id, [
            'product_id' => $product->id,
            'attribute_id' => $otherAttribute->id,
            'attribute_values_id' => $productAttribute->attribute_values_id,
            'sort_order' => 1,
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_product_attribute_with_attribute_value_from_other_attribute(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $productAttribute = ProductAttribute::factory()->create([
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
        ]);

        $otherAttribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $otherAttributeValue = AttributeValue::factory()->create([
            'attribute_id' => $otherAttribute->id,
        ]);

        // Act
        $response = $this->putJson('/api/internal/product_attributes/' . $productAttribute->id, [
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $otherAttributeValue->id,
            'sort_order' => 1,
        ]);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_show_product_attribute(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $attribute->id,
        ]);

        $productAttribute = ProductAttribute::factory()->create([
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $attributeValue->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/product_attributes/' . $productAttribute->id);

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'id',
                'product_id',
                'attribute_id',
                'attribute_values_id',
               'copy',
                'sort_order',
                'created_at',
                'updated_at',
            ])
            ->assertJsonPath('id', $productAttribute->id)
            ->assertJsonPath('product_id', $product->id)
            ->assertJsonPath('attribute_id', $attribute->id)
            ->assertJsonPath('attribute_values_id', $attributeValue->id)
            ->assertJsonPath('cabinet_id', $this->cabinet->id);
    }

    public function test_cannot_show_product_attribute_from_other_cabinet(): void
    {
        // Arrange
        $otherProduct = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $otherAttribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $otherAttributeValue = AttributeValue::factory()->create([
            'attribute_id' => $otherAttribute->id,
        ]);

        $otherProductAttribute = ProductAttribute::factory()->create([
            'product_id' => $otherProduct->id,
            'attribute_id' => $otherAttribute->id,
            'attribute_values_id' => $otherAttributeValue->id,
        ]);

        // Act
        $response = $this->getJson('/api/internal/product_attributes/' . $otherProductAttribute->id);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_product_attribute(): void
    {
        // Act
        $response = $this->getJson('/api/internal/product_attributes/' . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_product_attribute(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $attribute->id,
        ]);

        $productAttribute = ProductAttribute::factory()->create([
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $attributeValue->id,
        ]);

        // Act
        $response = $this->deleteJson('/api/internal/product_attributes/' . $productAttribute->id);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseMissing('product_attributes', [
            'id' => $productAttribute->id,
        ]);
    }

    public function test_cannot_delete_product_attribute_from_other_cabinet(): void
    {
        // Arrange
        $otherProduct = Product::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $otherAttribute = Attribute::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $otherAttributeValue = AttributeValue::factory()->create([
            'attribute_id' => $otherAttribute->id,
        ]);

        $otherProductAttribute = ProductAttribute::factory()->create([
            'product_id' => $otherProduct->id,
            'attribute_id' => $otherAttribute->id,
            'attribute_values_id' => $otherAttributeValue->id,
        ]);

        // Act
        $response = $this->deleteJson('/api/internal/product_attributes/' . $otherProductAttribute->id);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('product_attributes', [
            'id' => $otherProductAttribute->id,
        ]);
    }

    public function test_cannot_delete_non_existent_product_attribute(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/product_attributes/' . $this->faker->uuid());

        // Assert
        $response->assertNotFound();
    }
}
