<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\UserSettings;
use Illuminate\Support\Facades\Storage;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserSettingTest extends TestCase
{
    use RefreshDatabase;

    public function test_view_user_settings_no_auth(): void
    {
        $response = $this->get('/api/internal/user_settings');

        $response->assertStatus(401);
    }

    public function test_view_user_settings(): void
    {
        $user = User::factory()->create();
        UserSettings::factory()->create([
            'user_id' => $user->id
        ]);

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->get('/api/internal/user_settings');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'user_settings' => [
                    'id', 'user_id'
                ]
            ])
            ->assertJsonPath('user_settings.user_id', $user->id); // Проверяем, что user_id совпадает
    }

    public function test_update_user_settings_with_all_fields(): void
    {
        $user = User::factory()->create();
        UserSettings::factory()->create([
            'user_id' => $user->id
        ]);

        $data = [
            'lastname' => 'Иванов',
            'firstname' => 'Иван',
            'patronymic' => 'Иванович',
            'tel' => '88005553535',
            'email' => '<EMAIL>',
            'password' => 'password',
            'inn' => '1234567890',
            'language' => 'ru',
            'printing_documents' => '1',
            'additional_fields' => 1,
            'start_screen' => '1',
            'update_reports_automatically' => true,
            'signature_sent_emails' => '1',
            'image' => Storage::get('test/test.jpeg')
        ];

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->put('/api/internal/user_settings', $data);

        //$response->dump();
        $response->assertNoContent();

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'lastname' => $data['lastname'],
            'firstname' => $data['firstname'],
            'patronymic' => $data['patronymic'],
            'tel' => $data['tel'],
            'email' => $data['email'],
        ]);

        $this->assertDatabaseHas('user_settings', [
            'user_id' => $user->id,
            'inn' => $data['inn'],
            'language' => $data['language'],
            'printing_documents' => $data['printing_documents'],
            'additional_fields' => $data['additional_fields'],
            'start_screen' => $data['start_screen'],
            'update_reports_automatically' => $data['update_reports_automatically'],
            'signature_sent_emails' => $data['signature_sent_emails'],
        ]);
    }

    public function test_update_user_settings_with_only_required_fields(): void
    {
        $user = User::factory()->create();
        UserSettings::factory()->create([
            'user_id' => $user->id
        ]);

        $data = [
            'firstname' => 'Иван',
        ];
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->put('/api/internal/user_settings', $data);

        $response->assertNoContent();
    }

    public function test_update_user_settings_discounts(): void
    {
        $user = User::factory()->create();
        UserSettings::factory()->create([
            'user_id' => $user->id
        ]);

        $data = [
            'discount' => 'asd',
        ];
        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->put('/api/internal/user_settings/' . $user->id . '/discount', $data);

        $response->assertNoContent();

        $this->assertDatabaseHas('user_settings', [
            'user_id' => $user->id,
            'discount' => $data['discount'],
        ]);
    }

    public function test_update_user_settings_discount_without_data(): void
    {
        $user = User::factory()->create();
        UserSettings::factory()->create([
            'user_id' => $user->id
        ]);

        $response = $this->withHeaders([
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $user->createToken('Api-token')->plainTextToken,
        ])->put('/api/internal/user_settings/' . $user->id . '/discount');

        $response->assertNoContent();

        $this->assertDatabaseHas('user_settings', [
            'user_id' => $user->id,
            'discount' => null,
        ]);
    }
}
