<?php

namespace Tests\Feature;

use App\Enums\Api\Internal\AgeCategoryEnum;
use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\LegalEntityTaxation;
use App\Enums\Api\Internal\ProductThresholdsEnum;
use App\Enums\Api\Internal\TargetGenderEnum;
use App\Enums\Api\Internal\TypeAccountingEnum;
use App\Enums\Api\Internal\TypeProductEnum;
use App\Enums\Api\Internal\TypeProductIndicationSubjectCalculationEnum;
use App\Enums\Api\Internal\TypeProductionEnum;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\Barcode;
use App\Models\Brand;
use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetPrice;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\Country;
use App\Models\Department;
use App\Models\Employee;
use App\Models\MeasurementUnit;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Str;
use Tests\TestCase;

class ProductTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected User $user;
    protected Cabinet $cabinet;
    protected Cabinet $otherCabinet;
    protected Employee $employee;
    protected Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_products_list(): void
    {
        // Arrange
        $products = Product::factory(2)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $otherProducts = Product::factory(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => []
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ])
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
        }
    }

    public function test_cannot_get_products_without_cabinet_id(): void
    {
        // Act
        $response = $this->getJson('/api/internal/products');

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors(['cabinet_id']);
    }

    public function test_cannot_get_products_from_other_cabinet(): void
    {
        // Arrange
        Product::factory(2)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertForbidden();
    }

    public function test_can_filter_products_by_search(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Product ABC',
            'code' => '123',
            'article' => 'ART123',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Another Product',
            'code' => 'ABC456',
            'article' => 'ART456',
        ]);

        $product3 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Different Item',
            'code' => '789',
            'article' => 'ART789',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'ABC'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(2, 'data');

        $productIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($product1->id, $productIds);
        $this->assertContains($product2->id, $productIds);
        $this->assertNotContains($product3->id, $productIds);
    }

    public function test_can_filter_products_by_show_only_archived(): void
    {
        // Arrange
        $archivedProduct = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => now(),
        ]);

        $activeProduct = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => null,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'show_only' => [
                    'value' => 'archived'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($archivedProduct->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_show_only_common(): void
    {
        // Arrange
        $archivedProduct = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => now(),
        ]);

        $activeProduct = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => null,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'show_only' => [
                    'value' => 'common'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($activeProduct->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_empty_description(): void
    {
        // Arrange
        $productWithoutDescription = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => null,
        ]);

        $productWithDescription = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Some description',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($productWithoutDescription->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_not_empty_description(): void
    {
        // Arrange
        $productWithoutDescription = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => null,
        ]);

        $productWithDescription = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Some description',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($productWithDescription->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_employee_owners(): void
    {
        // Arrange
        $anotherEmployee = Employee::factory()->create();

        $productWithEmployee = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
        ]);

        $productWithAnotherEmployee = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $anotherEmployee->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'employee_owners' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => [$this->employee->id]
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($productWithEmployee->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_department_owners(): void
    {
        // Arrange
        $anotherDepartment = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $productWithDepartment = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
        ]);

        $productWithAnotherDepartment = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $anotherDepartment->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'department_owners' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => [$this->department->id]
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($productWithDepartment->id, $response->json('data.0.id'));
    }

    public function test_returns_empty_collection_when_no_products(): void
    {
        // Arrange
        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(0, 'data')
            ->assertJson([
                'data' => [],
                'meta' => [
                    'total' => 0,
                ]
            ]);
    }

    public function test_can_filter_products_by_type_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'type' => TypeProductEnum::PRODUCT->value,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'type' => TypeProductEnum::SERVICE->value,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => TypeProductEnum::PRODUCT->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_type_not_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'type' => TypeProductEnum::PRODUCT->value,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'type' => TypeProductEnum::SERVICE->value,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => [
                    'condition' => FilterConditionEnum::NOT_IN->value,
                    'value' => TypeProductEnum::PRODUCT->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_type_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(0, 'data');
    }

    public function test_can_filter_products_by_type_not_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(2, 'data');
    }

    public function test_can_filter_products_by_title_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Title One',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Title Two',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'title' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => 'One'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_title_not_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Title One',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Title Two',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'title' => [
                    'condition' => FilterConditionEnum::NOT_IN->value,
                    'value' => 'One'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk();

        foreach ($response->json('data') as $item) {
            $this->assertNotEquals('One', $item['title']);
        }
    }

    public function test_can_filter_products_by_title_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Title One',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'title' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk();

        foreach ($response->json('data') as $item) {
            $this->assertNull($item['title']);
        }
    }

    public function test_can_filter_products_by_title_not_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Title One',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'title' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(2, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_article_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'article' => 'ART001',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'article' => 'ART002',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'article' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => 'ART001'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_article_not_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'article' => 'ART001',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'article' => 'ART002',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'article' => [
                    'condition' => FilterConditionEnum::NOT_IN->value,
                    'value' => 'ART001'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_article_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'article' => null,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'article' => 'ART001',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'article' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_article_not_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'article' => null,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'article' => 'ART001',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'article' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_code_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE001',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE002',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => 'CODE001'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_code_not_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE001',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE002',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => FilterConditionEnum::NOT_IN->value,
                    'value' => 'CODE001'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_code_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => null,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE001',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_code_not_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => null,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'CODE001',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_external_code_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'external_code' => 'EXT001',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'external_code' => 'EXT002',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'external_code' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => 'EXT001'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_external_code_not_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'external_code' => 'EXT001',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'external_code' => 'EXT002',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'external_code' => [
                    'condition' => FilterConditionEnum::NOT_IN->value,
                    'value' => 'EXT001'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_external_code_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'external_code' => null,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'external_code' => 'EXT001',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'external_code' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_external_code_not_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'external_code' => null,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'external_code' => 'EXT001',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'external_code' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_barcode_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        Barcode::factory()->create([
            'barcodable_id' => $product1->id,
            'barcodable_type' => Product::class,
            'value' => 'BAR001',
        ]);

        Barcode::factory()->create([
            'barcodable_id' => $product2->id,
            'barcodable_type' => Product::class,
            'value' => 'BAR002',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'barcode' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => 'BAR001'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_barcode_not_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        Barcode::factory()->create([
            'barcodable_id' => $product1->id,
            'barcodable_type' => Product::class,
            'value' => 'BAR001',
        ]);

        Barcode::factory()->create([
            'barcodable_id' => $product2->id,
            'barcodable_type' => Product::class,
            'value' => 'BAR002',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'barcode' => [
                    'condition' => FilterConditionEnum::NOT_IN->value,
                    'value' => 'BAR001'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_barcode_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        Barcode::factory()->create([
            'barcodable_id' => $product2->id,
            'barcodable_type' => Product::class,
            'value' => 'BAR001',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'barcode' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_barcode_not_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        Barcode::factory()->create([
            'barcodable_id' => $product2->id,
            'barcodable_type' => Product::class,
            'value' => 'BAR001',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'barcode' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_description_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Description One',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Description Two',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => 'One'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_description_not_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Description One',
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Description Two',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::NOT_IN->value,
                    'value' => 'One'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_description_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => null,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Description One',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_description_not_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => null,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'description' => 'Description One',
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'description' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_packing_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'packing' => [
                    'condition' => FilterConditionEnum::IN->value,
                    'value' => 'One'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_packing_not_in(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'packing' => [
                    'condition' => FilterConditionEnum::NOT_IN->value,
                    'value' => 'One'
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_packing_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'packing' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product1->id, $response->json('data.0.id'));
    }

    public function test_can_filter_products_by_packing_not_empty(): void
    {
        // Arrange
        $product1 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $product2 = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $query = http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'packing' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]);

        // Act
        $response = $this->getJson("/api/internal/products?{$query}");

        // Assert
        $response->assertOk()
            ->assertJsonCount(1, 'data');

        $this->assertEquals($product2->id, $response->json('data.0.id'));
    }

    public function test_can_create_product_with_minimal_required_fields(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => TypeProductEnum::PRODUCT->value,
            'code' => 'TEST001'
        ];

        // Act
        $response = $this->postJson('/api/internal/products', $data);

        // Assert
        $response->assertCreated()->assertJsonStructure(['id']);

        $this->assertDatabaseHas('products', [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => TypeProductEnum::PRODUCT->value,
            'code' => 'TEST001'
        ]);
    }

    public function test_cannot_create_product_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/products', []);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'cabinet_id',
                'department_id',
                'employee_id',
                'title',
                'short_title',
                'type',
                'code'
            ]);
    }

    public function test_cannot_create_product_in_other_cabinet(): void
    {
        // Arrange
        $otherCabinet = Cabinet::factory()->create();

        $data = [
            'cabinet_id' => $otherCabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => TypeProductEnum::PRODUCT->value,
            'code' => 'TEST001'
        ];

        // Act
        $response = $this->postJson('/api/internal/products', $data);

        // Assert
        $response->assertForbidden();
    }

    public function test_can_create_product_with_all_fields(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $category = ProductCategory::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $brand = Brand::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $measurementUnit = MeasurementUnit::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $country = Country::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        );

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'contractor_id' => $contractor->id,
            'category_id' => $category->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => TypeProductEnum::PRODUCT->value,
            'description' => 'Test description',
            'short_description' => 'Short desc',
            'discounts_retail_sales' => true,
            'country_id' => $country->id,
            'article' => 'ART001',
            'code' => 'TEST001',
            'external_code' => 'EXT001',
            'measurement_unit_id' => $measurementUnit->id,
            'brand_id' => $brand->id,
            'dimensions' => [
                'length' => 10,
                'width' => 20,
                'height' => 30,
                'weight' => 1.5,
                'volume' => 6000
            ],
            'tax_system' => LegalEntityTaxation::osno->value,
            'indication_subject_calculation' => TypeProductIndicationSubjectCalculationEnum::PRODUCT->value,
            'type_accounting' => TypeAccountingEnum::ACCOUNTING_BY_SERIAL_NUMBERS->value,
            'accounting_series' => true,
            'target_gender' => TargetGenderEnum::UNISEX->value,
            'type_production' => TypeProductionEnum::IMPORTED_RUSSIAN->value,
            'age_category' => AgeCategoryEnum::ADULT->value,
            'set' => true,
            'partial_sale' => false,
            'model' => 'MODEL-X',
            'traceable' => true
        ];

        // Act
        $response = $this->postJson('/api/internal/products', $data);

        // Assert
        $response->assertCreated()->assertJsonStructure(['id']);

        $this->assertDatabaseHas('products', [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'contractor_id' => $contractor->id,
            'category_id' => $category->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => TypeProductEnum::PRODUCT->value,
            'description' => 'Test description',
            'short_description' => 'Short desc',
            'discounts_retail_sales' => true,
            'country_id' => $country->id,
            'article' => 'ART001',
            'code' => 'TEST001',
            'external_code' => 'EXT001',
            'measurement_unit_id' => $measurementUnit->id,
            'brand_id' => $brand->id
        ]);

        $this->assertDatabaseHas('product_accounting_features', [
            'age_category' => AgeCategoryEnum::ADULT->value,
            'set' => true,
            'partial_sale' => false,
            'model' => 'MODEL-X',
            'traceable' => true
        ]);
    }

    public function test_can_create_product_with_sale_prices(): void
    {
        // Arrange
        $cabinetPrice = CabinetPrice::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $currency = CabinetCurrency::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => TypeProductEnum::PRODUCT->value,
            'code' => 'TEST001',
            'sale_price' => [
                [
                    'cabinet_price_id' => $cabinetPrice->id,
                    'amount' => 100.50,
                    'currency_id' => $currency->id,
                    'sort' => 1
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/products', $data);

        // Assert
        $response->assertCreated();

        $productId = $response->json('data');

        $this->assertDatabaseHas('product_prices', [
            'product_id' => $productId,
            'cabinet_price_id' => $cabinetPrice->id,
            'amount' => 100.50,
            'currency_id' => $currency->id,
            'sort' => 1
        ]);
    }

    public function test_can_create_product_with_attributes(): void
    {
        // Arrange
        $attribute = Attribute::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $attributeValue = AttributeValue::factory()->create([
            'attribute_id' => $attribute->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => TypeProductEnum::PRODUCT->value,
            'code' => 'TEST001',
            'attributes' => [
                [
                    'attribute_id' => $attribute->id,
                    'attribute_values_id' => $attributeValue->id,
                    'sort_order' => 1
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/products', $data);

        // Assert
        $response->assertCreated();

        $productId = $response->json('id');

        $this->assertDatabaseHas('product_attributes', [
            'product_id' => $productId,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $attributeValue->id,
            'sort_order' => 1
        ]);
    }

    public function test_can_create_product_with_thresholds(): void
    {
        // Arrange
        $warehouse = Warehouse::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => TypeProductEnum::PRODUCT->value,
            'code' => 'TEST001',
            'thresholds' => [
                'type' => ProductThresholdsEnum::SET_FOR_EACH_WAREHOUSE->value,
                'warehouses' => [
                    [
                        'warehouse_id' => $warehouse->id,
                        'threshold_count' => 10
                    ]
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/products', $data);

        // Assert
        $response->assertCreated();

        $productId = $response->json('id');

        $this->assertDatabaseHas('product_threshold_warehouses', [
            'product_id' => $productId,
            'warehouse_id' => $warehouse->id,
            'threshold_count' => 10
        ]);
    }

    public function test_cannot_create_product_with_invalid_enum_values(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => 999, // Invalid enum value
            'code' => 'TEST001',
            'tax_system' => 'INVALID_TAX',
            'type_accounting' => 'INVALID_TYPE',
            'target_gender' => 'INVALID_GENDER',
            'type_production' => 'INVALID_PRODUCTION',
            'age_category' => 'INVALID_AGE'
        ];

        // Act
        $response = $this->postJson('/api/internal/products', $data);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'type',
                'tax_system',
                'type_accounting',
                'target_gender',
                'type_production',
                'age_category'
            ]);
    }

    public function test_can_update_product_with_minimal_required_fields(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create();

        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Old Title',
            'short_title' => 'Old Short Title',
            'code' => 'OLD-001',
            'type' => TypeProductEnum::PRODUCT->value
        ]);

        $data = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'New Title',
            'short_title' => 'New Short Title',
            'code' => 'NEW-001',
            'type' => TypeProductEnum::SERVICE->value
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}", $data);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'title' => 'New Title',
            'short_title' => 'New Short Title',
            'code' => 'NEW-001',
            'type' => TypeProductEnum::SERVICE->value
        ]);
    }

    public function test_cannot_update_product_without_required_fields(): void
    {
        // Arrange
        $product = Product::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'title' => '',
            'short_title' => '',
            'code' => '',
            'type' => 999 // несуществующий тип
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}", $data);

        // Assert
        $response->assertUnprocessable()
            ->assertJsonValidationErrors([
                'department_id',
                'employee_id',
                'title',
                'short_title',
                'code',
                'type'
            ]);
    }

    public function test_cannot_update_product_in_other_cabinet(): void
    {
        // Arrange
        $product = Product::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        $data = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'New Title',
            'short_title' => 'New Short Title',
            'code' => 'NEW-001',
            'type' => TypeProductEnum::SERVICE->value
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}", $data);

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'cabinet_id' => $this->otherCabinet->id,
            'title' => $product->title
        ]);
    }

    public function test_can_update_product_with_all_fields(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $category = ProductCategory::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $brand = Brand::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $measurementUnit = MeasurementUnit::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $country = Country::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $product = Product::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'contractor_id' => $contractor->id,
            'category_id' => $category->id,
            'title' => 'Updated Product',
            'short_title' => 'Updated',
            'type' => TypeProductEnum::PRODUCT->value,
            'description' => 'Updated description',
            'short_description' => 'Short desc updated',
            'discounts_retail_sales' => true,
            'country_id' => $country->id,
            'article' => 'ART002',
            'code' => 'TEST002',
            'external_code' => 'EXT002',
            'measurement_unit_id' => $measurementUnit->id,
            'brand' => [
                'id' => $brand->id,
                'title' => $brand->title
            ],
            'dimensions' => [
                'length' => 15,
                'width' => 25,
                'height' => 35,
                'weight' => 2.5,
                'volume' => 7000
            ],
            'tax_system' => LegalEntityTaxation::osno->value,
            'indication_subject_calculation' => TypeProductIndicationSubjectCalculationEnum::PRODUCT->value,
            'type_accounting' => TypeAccountingEnum::ACCOUNTING_BY_SERIAL_NUMBERS->value,
            'accounting_series' => true,
            'target_gender' => TargetGenderEnum::UNISEX->value,
            'type_production' => TypeProductionEnum::IMPORTED_RUSSIAN->value,
            'age_category' => AgeCategoryEnum::ADULT->value,
            'set' => true,
            'partial_sale' => false,
            'model' => 'MODEL-Y',
            'traceable' => true
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}", $data);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'contractor_id' => $contractor->id,
            'category_id' => $category->id,
            'title' => 'Updated Product',
            'short_title' => 'Updated',
            'type' => TypeProductEnum::PRODUCT->value,
            'description' => 'Updated description',
            'short_description' => 'Short desc updated',
            'discounts_retail_sales' => true,
            'country_id' => $country->id,
            'article' => 'ART002',
            'code' => 'TEST002',
            'external_code' => 'EXT002',
            'measurement_unit_id' => $measurementUnit->id,
            'brand_id' => $brand->id
        ]);

        $this->assertDatabaseHas('product_accounting_features', [
            'product_id' => $product->id,
            'age_category' => AgeCategoryEnum::ADULT->value,
            'set' => true,
            'partial_sale' => false,
            'model' => 'MODEL-Y',
            'traceable' => true
        ]);
    }

    public function test_can_update_product_with_sale_prices(): void
    {
        // Arrange
        $product = Product::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $cabinetPrice = CabinetPrice::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $currency = CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Updated Product',
            'short_title' => 'Updated',
            'type' => TypeProductEnum::PRODUCT->value,
            'code' => 'TEST002',
            'sale_price' => [
                [
                    'cabinet_price_id' => $cabinetPrice->id,
                    'amount' => 150,
                    'currency_id' => $currency->id,
                    'sort' => 1
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}", $data);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('product_prices', [
            'product_id' => $product->id,
            'cabinet_price_id' => $cabinetPrice->id,
            'amount' => 150,
            'currency_id' => $currency->id,
            'sort' => 1
        ]);
    }

    public function test_can_update_product_with_attributes(): void
    {
        // Arrange
        $product = Product::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $attribute = Attribute::factory()->create(['cabinet_id' => $this->cabinet->id]);
        $attributeValue = AttributeValue::factory()->create();

        $data = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Updated Product',
            'short_title' => 'Updated',
            'type' => TypeProductEnum::PRODUCT->value,
            'code' => 'TEST002',
            'attributes' => [
                [
                    'attribute_id' => $attribute->id,
                    'attribute_values_id' => $attributeValue->id,
                    'sort_order' => 1
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}", $data);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('product_attributes', [
            'product_id' => $product->id,
            'attribute_id' => $attribute->id,
            'attribute_values_id' => $attributeValue->id,
            'sort_order' => 1
        ]);
    }

    public function test_can_update_product_with_thresholds(): void
    {
        // Arrange
        $product = Product::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $warehouse = Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id]);

        $data = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'title' => 'Updated Product',
            'short_title' => 'Updated',
            'type' => TypeProductEnum::PRODUCT->value,
            'code' => 'TEST002',
            'thresholds' => [
                'type' => ProductThresholdsEnum::SET_FOR_EACH_WAREHOUSE->value,
                'warehouses' => [
                    [
                        'warehouse_id' => $warehouse->id,
                        'threshold_count' => 15
                    ]
                ]
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/products/{$product->id}", $data);

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseHas('product_threshold_warehouses', [
            'product_id' => $product->id,
            'warehouse_id' => $warehouse->id,
            'threshold_count' => 15
        ]);
    }

    public function test_can_show_product(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Product',
            'short_title' => 'Test',
            'type' => TypeProductEnum::PRODUCT->value
        ]);

        // Act
        $response = $this->getJson("/api/internal/products/{$product->id}/show");

        // Assert
        $response->assertOk()
            ->assertJsonFragment([
                'id' => $product->id,
                'title' => 'Test Product',
                'short_title' => 'Test',
                'type' => TypeProductEnum::PRODUCT->value
            ]);
    }

    public function test_cannot_show_product_from_other_cabinet(): void
    {
        // Arrange
        $product = Product::factory()->create(['cabinet_id' => $this->otherCabinet->id]);

        // Act
        $response = $this->getJson("/api/internal/products/{$product->id}/show");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_product(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create();
        $nonExistentId = Str::orderedUuid();

        // Act
        $response = $this->getJson("/api/internal/products/{$nonExistentId}/show");

        // Assert
        $response->assertNotFound();
    }

    public function test_can_delete_product(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
        // Act
        $response = $this->deleteJson("/api/internal/products/{$product->id}");

        // Assert
        $response->assertNoContent();

        $this->assertDatabaseMissing('products', [
            'id' => $product->id
        ]);
    }

    public function test_cannot_delete_product_from_other_cabinet(): void
    {
        // Arrange
        $product = Product::factory()->create();

        // Act
        $response = $this->deleteJson("/api/internal/products/{$product->id}");

        // Assert
        $response->assertNotFound();

        $this->assertDatabaseHas('products', [
            'id' => $product->id
        ]);
    }

    public function test_cannot_delete_non_existent_product(): void
    {
        // Arrange
        $cabinet = Cabinet::factory()->create();
        $nonExistentId = Str::orderedUuid();

        // Act
        $response = $this->deleteJson("/api/internal/products/{$nonExistentId}");

        // Assert
        $response->assertNotFound();
    }
}
