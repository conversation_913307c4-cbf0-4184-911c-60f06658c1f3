<?php

namespace Database\Factories;

use App\Models\Warehouse;
use App\Models\WarehouseGroup;
use App\Models\WarehouseCellSize;
use App\Enums\Api\Internal\WarehouseCellTypeEnum;
use Illuminate\Database\Eloquent\Factories\Factory;
use App\Enums\Api\Internal\WarehouseCellAddressSeparatorEnum;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseCell>
 */
class WarehouseCellFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'group_id' => WarehouseGroup::factory(),
            'warehouse_id' => Warehouse::factory(),
            'type' => $this->faker->randomElement(WarehouseCellTypeEnum::cases())->value,
            'address' => $this->faker->address,
            'description' => $this->faker->text,
            'section' => $this->faker->word,
            'line' => $this->faker->word,
            'rack' => $this->faker->word,
            'tier' => $this->faker->word,
            'position' => $this->faker->word,
            'separator' => $this->faker->randomElement(WarehouseCellAddressSeparatorEnum::cases())->value,
            'availability_level' => random_int(0,999),
            'circumvention_order' => random_int(0,999),
            'size_id' => WarehouseCellSize::factory(),
            'filling_volume' => random_int(0,999),
            'filling_weight' => random_int(0,999),
            'stocktake' => random_int(0,999),
            'recalculate_days' => random_int(0,999),
        ];
    }
}
